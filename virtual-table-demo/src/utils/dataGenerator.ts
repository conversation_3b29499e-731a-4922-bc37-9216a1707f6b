import { TableRow } from '../types';

// 模拟数据生成器
const firstNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const lastNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const cities = [
  'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia',
  'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville',
  'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco', 'Indianapolis', 'Seattle',
  'Denver', 'Washington', 'Boston', 'El Paso', 'Nashville', 'Detroit', 'Oklahoma City'
];

const companies = [
  'Apple Inc.', 'Microsoft', 'Amazon', 'Google', 'Meta', 'Tesla', 'Netflix', 'Adobe',
  'Salesforce', 'Oracle', 'IBM', 'Intel', 'Cisco', 'PayPal', 'Uber', 'Airbnb',
  'Spotify', 'Twitter', 'LinkedIn', 'Zoom', 'Slack', 'Dropbox', 'Square', 'Stripe'
];

const departments = [
  'Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations', 'Design',
  'Product', 'Legal', 'Support', 'Research', 'Quality Assurance'
];

const statuses: Array<'active' | 'inactive' | 'pending'> = ['active', 'inactive', 'pending'];

// 生成随机数据的辅助函数
function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomDate(start: Date, end: Date): string {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return date.toISOString().split('T')[0];
}

// 生成单行数据
export function generateRow(id: number): TableRow {
  const firstName = getRandomItem(firstNames);
  const lastName = getRandomItem(lastNames);
  const name = `${firstName} ${lastName}`;
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${getRandomItem(companies).toLowerCase().replace(/[^a-z]/g, '')}.com`;
  
  return {
    id,
    name,
    email,
    age: getRandomNumber(22, 65),
    city: getRandomItem(cities),
    company: getRandomItem(companies),
    salary: getRandomNumber(40000, 200000),
    department: getRandomItem(departments),
    joinDate: getRandomDate(new Date(2015, 0, 1), new Date()),
    status: getRandomItem(statuses)
  };
}

// 生成大量数据
export function generateData(count: number): TableRow[] {
  console.log(`Generating ${count.toLocaleString()} rows of data...`);
  const startTime = performance.now();
  
  const data: TableRow[] = [];
  for (let i = 1; i <= count; i++) {
    data.push(generateRow(i));
    
    // 每生成10万条数据输出一次进度
    if (i % 100000 === 0) {
      console.log(`Generated ${i.toLocaleString()} rows...`);
    }
  }
  
  const endTime = performance.now();
  console.log(`Data generation completed in ${(endTime - startTime).toFixed(2)}ms`);
  
  return data;
}

// 分批生成数据（用于避免阻塞UI）
export async function generateDataAsync(count: number, batchSize: number = 10000): Promise<TableRow[]> {
  console.log(`Generating ${count.toLocaleString()} rows of data in batches...`);
  const startTime = performance.now();
  
  const data: TableRow[] = [];
  
  for (let i = 0; i < count; i += batchSize) {
    const currentBatchSize = Math.min(batchSize, count - i);
    
    // 生成当前批次的数据
    for (let j = 0; j < currentBatchSize; j++) {
      data.push(generateRow(i + j + 1));
    }
    
    console.log(`Generated ${Math.min(i + batchSize, count).toLocaleString()} / ${count.toLocaleString()} rows...`);
    
    // 让出控制权给浏览器，避免阻塞UI
    await new Promise(resolve => setTimeout(resolve, 0));
  }
  
  const endTime = performance.now();
  console.log(`Async data generation completed in ${(endTime - startTime).toFixed(2)}ms`);
  
  return data;
}
