.virtual-table-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.virtual-table-header {
  background: #f8f9fa;
  border-bottom: 2px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.virtual-table-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.header-row {
  height: 50px;
  font-weight: 600;
  background: #f8f9fa;
  color: #333;
}

.data-row {
  cursor: pointer;
  background: white;
}

.data-row:hover {
  background-color: #f5f5f5;
}

.data-row.even {
  background-color: #fafafa;
}

.data-row.even:hover {
  background-color: #f0f0f0;
}

.virtual-table-cell {
  padding: 12px 16px;
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 1.4;
}

.virtual-table-cell:last-child {
  border-right: none;
}

.header-cell {
  background: #f8f9fa;
  color: #555;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.data-cell {
  color: #333;
}

.virtual-table-body {
  position: relative;
  overflow: auto;
}

.virtual-scroll-container {
  position: relative;
}

.virtual-table-footer {
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  font-size: 12px;
  color: #666;
  text-align: right;
}

.virtual-table-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.loading-spinner {
  font-size: 16px;
  color: #666;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 状态标签样式 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-inactive {
  background: #ffebee;
  color: #c62828;
}

.status-pending {
  background: #fff3e0;
  color: #ef6c00;
}

/* 薪资格式化样式 */
.salary-cell {
  font-weight: 500;
  color: #1976d2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .virtual-table-cell {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .header-cell {
    font-size: 11px;
  }
  
  .virtual-table-footer {
    font-size: 11px;
    padding: 6px 12px;
  }
}

/* 滚动条样式 */
.virtual-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.virtual-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.virtual-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 选中行样式 */
.data-row.selected {
  background-color: #e3f2fd !important;
  border-color: #2196f3;
}

.data-row.selected:hover {
  background-color: #bbdefb !important;
}
