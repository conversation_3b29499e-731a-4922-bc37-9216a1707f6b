import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { VirtualTableProps, VirtualScrollState, TableRow } from '../types';
import './VirtualTable.css';

const VirtualTable: React.FC<VirtualTableProps> = ({
  data,
  columns,
  height,
  rowHeight = 50,
  bufferSize = 10,
  onRowClick,
  loading = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollState, setScrollState] = useState<VirtualScrollState>({
    scrollTop: 0,
    startIndex: 0,
    endIndex: 0,
    visibleData: []
  });

  // 计算可视区域的行数
  const visibleRowCount = Math.ceil(height / rowHeight);

  // 计算虚拟滚动的参数
  const calculateVisibleRange = useCallback((scrollTop: number) => {
    const startIndex = Math.max(0, Math.floor(scrollTop / rowHeight) - bufferSize);
    const endIndex = Math.min(
      data.length - 1,
      startIndex + visibleRowCount + bufferSize * 2
    );
    
    return { startIndex, endIndex };
  }, [data.length, rowHeight, visibleRowCount, bufferSize]);

  // 处理滚动事件
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = event.currentTarget.scrollTop;
    const { startIndex, endIndex } = calculateVisibleRange(scrollTop);
    
    setScrollState(prevState => {
      // 只有当范围发生变化时才更新状态
      if (prevState.startIndex !== startIndex || prevState.endIndex !== endIndex) {
        const visibleData = data.slice(startIndex, endIndex + 1);
        return {
          scrollTop,
          startIndex,
          endIndex,
          visibleData
        };
      }
      return { ...prevState, scrollTop };
    });
  }, [calculateVisibleRange, data]);

  // 初始化可视数据
  useEffect(() => {
    const { startIndex, endIndex } = calculateVisibleRange(0);
    const visibleData = data.slice(startIndex, endIndex + 1);
    
    setScrollState({
      scrollTop: 0,
      startIndex,
      endIndex,
      visibleData
    });
  }, [data, calculateVisibleRange]);

  // 计算总高度
  const totalHeight = data.length * rowHeight;

  // 计算偏移量
  const offsetY = scrollState.startIndex * rowHeight;

  // 渲染表头
  const renderHeader = () => (
    <div className="virtual-table-header" style={{ position: 'sticky', top: 0, zIndex: 10 }}>
      <div className="virtual-table-row header-row">
        {columns.map((column) => (
          <div
            key={column.key}
            className="virtual-table-cell header-cell"
            style={{ 
              width: column.width,
              textAlign: column.align || 'left'
            }}
          >
            {column.title}
          </div>
        ))}
      </div>
    </div>
  );

  // 渲染数据行
  const renderRow = (row: TableRow, index: number) => (
    <div
      key={row.id}
      className={`virtual-table-row data-row ${index % 2 === 0 ? 'even' : 'odd'}`}
      onClick={() => onRowClick?.(row)}
      style={{
        height: rowHeight,
        transform: `translateY(${offsetY + index * rowHeight}px)`,
        position: 'absolute',
        width: '100%',
        left: 0
      }}
    >
      {columns.map((column) => (
        <div
          key={column.key}
          className="virtual-table-cell data-cell"
          style={{ 
            width: column.width,
            textAlign: column.align || 'left'
          }}
        >
          {column.render 
            ? column.render(row[column.key], row)
            : String(row[column.key])
          }
        </div>
      ))}
    </div>
  );

  // 计算表格总宽度
  const totalWidth = columns.reduce((sum, col) => sum + col.width, 0);

  if (loading) {
    return (
      <div className="virtual-table-loading" style={{ height }}>
        <div className="loading-spinner">Loading...</div>
      </div>
    );
  }

  return (
    <div className="virtual-table-container" style={{ height }}>
      {renderHeader()}
      <div
        ref={containerRef}
        className="virtual-table-body"
        style={{
          height: height - 50, // 减去表头高度
          overflowY: 'auto',
          overflowX: 'auto',
          position: 'relative'
        }}
        onScroll={handleScroll}
      >
        {/* 虚拟滚动容器 */}
        <div
          className="virtual-scroll-container"
          style={{
            height: totalHeight,
            width: totalWidth,
            position: 'relative'
          }}
        >
          {/* 渲染可视区域的行 */}
          {scrollState.visibleData.map((row, index) => 
            renderRow(row, index)
          )}
        </div>
      </div>
      
      {/* 显示统计信息 */}
      <div className="virtual-table-footer">
        <span>
          Total: {data.length.toLocaleString()} rows | 
          Visible: {scrollState.visibleData.length} rows |
          Range: {scrollState.startIndex} - {scrollState.endIndex}
        </span>
      </div>
    </div>
  );
};

export default VirtualTable;
