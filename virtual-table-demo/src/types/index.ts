import React from 'react';

// 表格数据行的接口
export interface TableRow {
  id: number;
  name: string;
  email: string;
  age: number;
  city: string;
  company: string;
  salary: number;
  department: string;
  joinDate: string;
  status: 'active' | 'inactive' | 'pending';
}

// 表格列的配置接口
export interface TableColumn {
  key: keyof TableRow;
  title: string;
  width: number;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: TableRow) => React.ReactNode;
}

// 虚拟滚动表格的属性接口
export interface VirtualTableProps {
  data: TableRow[];
  columns: TableColumn[];
  height: number;
  rowHeight?: number;
  bufferSize?: number;
  onRowClick?: (row: TableRow) => void;
  loading?: boolean;
}

// 虚拟滚动的内部状态接口
export interface VirtualScrollState {
  scrollTop: number;
  startIndex: number;
  endIndex: number;
  visibleData: TableRow[];
}
