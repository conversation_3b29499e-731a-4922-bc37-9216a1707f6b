.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  background: white;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.app-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.app-header p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.data-count-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.data-count-selector label {
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}

.count-btn {
  padding: 8px 16px;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 14px;
}

.count-btn:hover {
  border-color: #2196f3;
  background: #f3f8ff;
}

.count-btn.active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.count-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-btn {
  padding: 10px 20px;
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  font-size: 14px;
}

.refresh-btn:hover:not(:disabled) {
  background: #45a049;
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.app-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.selected-row-info {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.selected-row-info h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.selected-row-info pre {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  color: #333;
}

/* 状态标签样式 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-inactive {
  background: #ffebee;
  color: #c62828;
}

.status-pending {
  background: #fff3e0;
  color: #ef6c00;
}

/* 薪资格式化样式 */
.salary-cell {
  font-weight: 500;
  color: #1976d2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 16px;
  }

  .app-header h1 {
    font-size: 24px;
  }

  .controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .data-count-selector {
    justify-content: center;
  }

  .count-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .refresh-btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  .app-main {
    padding: 0 16px;
  }

  .selected-row-info {
    padding: 16px;
  }

  .selected-row-info pre {
    font-size: 11px;
    padding: 12px;
  }
}
