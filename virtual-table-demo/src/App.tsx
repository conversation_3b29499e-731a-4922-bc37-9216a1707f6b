import React, { useState, useEffect, useCallback } from 'react';
import VirtualTable from './components/VirtualTable';
import PerformanceMonitor from './components/PerformanceMonitor';
import { TableRow, TableColumn } from './types';
import { generateDataAsync } from './utils/dataGenerator';
import './App.css';

function App() {
  const [data, setData] = useState<TableRow[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRow, setSelectedRow] = useState<TableRow | null>(null);
  const [dataCount, setDataCount] = useState(100000); // 默认10万条数据
  const [visibleRows, setVisibleRows] = useState(0);

  // 定义表格列配置
  const columns: TableColumn[] = [
    {
      key: 'id',
      title: 'ID',
      width: 80,
      align: 'center'
    },
    {
      key: 'name',
      title: 'Name',
      width: 150,
      align: 'left'
    },
    {
      key: 'email',
      title: 'Email',
      width: 200,
      align: 'left'
    },
    {
      key: 'age',
      title: 'Age',
      width: 80,
      align: 'center'
    },
    {
      key: 'city',
      title: 'City',
      width: 120,
      align: 'left'
    },
    {
      key: 'company',
      title: 'Company',
      width: 150,
      align: 'left'
    },
    {
      key: 'salary',
      title: 'Salary',
      width: 120,
      align: 'right',
      render: (value: number) => (
        <span className="salary-cell">
          ${value.toLocaleString()}
        </span>
      )
    },
    {
      key: 'department',
      title: 'Department',
      width: 120,
      align: 'left'
    },
    {
      key: 'joinDate',
      title: 'Join Date',
      width: 120,
      align: 'center'
    },
    {
      key: 'status',
      title: 'Status',
      width: 100,
      align: 'center',
      render: (value: string) => (
        <span className={`status-badge status-${value}`}>
          {value}
        </span>
      )
    }
  ];

  // 生成数据
  const generateData = useCallback(async (count: number) => {
    setLoading(true);
    try {
      console.log(`Starting to generate ${count.toLocaleString()} rows...`);
      const newData = await generateDataAsync(count, 10000);
      setData(newData);
      console.log(`Successfully generated ${newData.length.toLocaleString()} rows`);
    } catch (error) {
      console.error('Error generating data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 处理行点击
  const handleRowClick = useCallback((row: TableRow) => {
    setSelectedRow(row);
    console.log('Selected row:', row);
  }, []);

  // 初始化数据
  useEffect(() => {
    generateData(dataCount);
  }, []);

  // 预设数据量选项
  const dataCountOptions = [
    { label: '10K', value: 10000 },
    { label: '50K', value: 50000 },
    { label: '100K', value: 100000 },
    { label: '500K', value: 500000 },
    { label: '1M', value: 1000000 },
    { label: '2M', value: 2000000 }
  ];

  return (
    <div className="app">
      <header className="app-header">
        <h1>百万级无限滚动表格组件 Demo</h1>
        <p>Request ID: 9b3f9b43-3be0-4e5c-9c7c-733dbc2d1f5b</p>

        <div className="controls">
          <div className="data-count-selector">
            <label>数据量: </label>
            {dataCountOptions.map(option => (
              <button
                key={option.value}
                className={`count-btn ${dataCount === option.value ? 'active' : ''}`}
                onClick={() => {
                  setDataCount(option.value);
                  generateData(option.value);
                }}
                disabled={loading}
              >
                {option.label}
              </button>
            ))}
          </div>

          <button
            className="refresh-btn"
            onClick={() => generateData(dataCount)}
            disabled={loading}
          >
            {loading ? '生成中...' : '重新生成数据'}
          </button>
        </div>
      </header>

      <main className="app-main">
        <div className="table-container">
          <VirtualTable
            data={data}
            columns={columns}
            height={600}
            rowHeight={50}
            bufferSize={10}
            onRowClick={handleRowClick}
            loading={loading}
          />
        </div>

        {selectedRow && (
          <div className="selected-row-info">
            <h3>选中的行信息:</h3>
            <pre>{JSON.stringify(selectedRow, null, 2)}</pre>
          </div>
        )}
      </main>

      <PerformanceMonitor
        dataCount={data.length}
        visibleRows={visibleRows}
      />
    </div>
  );
}

export default App;
