{"name": "vite", "version": "7.0.6", "type": "module", "license": "MIT", "author": "<PERSON>", "description": "Native-ESM powered web dev build tool", "bin": {"vite": "bin/vite.js"}, "keywords": ["frontend", "framework", "hmr", "dev-server", "build-tool", "vite"], "main": "./dist/node/index.js", "types": "./dist/node/index.d.ts", "exports": {".": "./dist/node/index.js", "./client": {"types": "./client.d.ts"}, "./module-runner": "./dist/node/module-runner.js", "./dist/client/*": "./dist/client/*", "./types/*": {"types": "./types/*"}, "./types/internal/*": null, "./package.json": "./package.json"}, "typesVersions": {"*": {"module-runner": ["dist/node/module-runner.d.ts"]}}, "imports": {"#module-sync-enabled": {"module-sync": "./misc/true.js", "default": "./misc/false.js"}}, "files": ["bin", "dist", "misc/**/*.js", "client.d.ts", "index.cjs", "index.d.cts", "types"], "engines": {"node": "^20.19.0 || >=22.12.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/vite"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://vite.dev", "funding": "https://github.com/vitejs/vite?sponsor=1", "//": "READ CONTRIBUTING.md to understand what to put under deps vs. devDeps!", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.6", "picomatch": "^4.0.3", "postcss": "^8.5.6", "rollup": "^4.40.0", "tinyglobby": "^0.2.14"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "devDependencies": {"@ampproject/remapping": "^2.3.0", "@babel/parser": "^7.28.0", "@jridgewell/trace-mapping": "^0.3.29", "@oxc-project/types": "0.77.0", "@polka/compression": "^1.0.0-next.25", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-dynamic-import-vars": "2.1.4", "@rollup/pluginutils": "^5.2.0", "@types/escape-html": "^1.0.4", "@types/pnpapi": "^0.0.5", "artichokie": "^0.3.2", "baseline-browser-mapping": "^2.5.5", "cac": "^6.7.14", "chokidar": "^3.6.0", "connect": "^3.7.0", "convert-source-map": "^2.0.0", "cors": "^2.8.5", "cross-spawn": "^7.0.6", "debug": "^4.4.1", "dep-types": "link:./src/types", "dotenv": "^17.2.0", "dotenv-expand": "^12.0.2", "es-module-lexer": "^1.7.0", "escape-html": "^1.0.3", "estree-walker": "^3.0.3", "etag": "^1.8.1", "host-validation-middleware": "^0.1.1", "http-proxy": "^1.18.1", "launch-editor-middleware": "^2.10.0", "lightningcss": "^1.30.1", "magic-string": "^0.30.17", "mlly": "^1.7.4", "mrmime": "^2.0.1", "nanoid": "^5.1.5", "open": "^10.2.0", "parse5": "^7.3.0", "pathe": "^2.0.3", "periscopic": "^4.0.2", "picocolors": "^1.1.1", "postcss-import": "^16.1.1", "postcss-load-config": "^6.0.1", "postcss-modules": "^6.0.1", "premove": "^4.0.0", "resolve.exports": "^2.0.3", "rolldown": "^1.0.0-beta.29", "rolldown-plugin-dts": "^0.14.1", "rollup-plugin-license": "^3.6.0", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "sirv": "^3.0.1", "strip-literal": "^3.0.0", "terser": "^5.43.1", "tsconfck": "^3.1.6", "types": "link:./types", "ufo": "^1.6.1", "ws": "^8.18.3"}, "peerDependencies": {"@types/node": "^20.19.0 || >=22.12.0", "jiti": ">=1.21.0", "less": "^4.0.0", "lightningcss": "^1.21.0", "sass": "^1.70.0", "sass-embedded": "^1.70.0", "stylus": ">=0.54.8", "sugarss": "^5.0.0", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "less": {"optional": true}, "sugarss": {"optional": true}, "lightningcss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}, "scripts": {"dev": "premove dist && pnpm build-bundle -w", "build": "premove dist && pnpm build-bundle && pnpm build-types", "build-bundle": "rolldown --config rolldown.config.ts", "build-types": "pnpm build-types-roll && pnpm build-types-check", "build-types-roll": "rolldown --config rolldown.dts.config.ts", "build-types-check": "tsc --project tsconfig.check.json", "typecheck": "tsc && tsc -p src/node", "lint": "eslint --cache --ext .ts src/**", "format": "prettier --write --cache --parser typescript \"src/**/*.ts\"", "generate-target": "tsx scripts/generateTarget.ts"}}